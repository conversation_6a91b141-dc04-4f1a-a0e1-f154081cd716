#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流式版本的后端API - 支持实时日志更新
"""

import os
import json
from config import ConfigManager
from llm_helper import LLMAPIClient
from template_driven_writing import TemplateDrivenWriting


def tdw_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
    """
    流式版本的模板驱动写作API
    
    Args:
        input_template: 模板文件路径
        input_resources_list: 参考资源文件路径列表
        output_file: 输出目录
        original_title: 原始标题
        new_title: 新标题
        
    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端
        llm_source = 'glm'  # qwen, deepseek; 只修改这里就行
        api_key = config.get('apis.{llm_source}.api_key'.format(llm_source=llm_source))
        base_url = config.get('apis.{llm_source}.base_url'.format(llm_source=llm_source))
        model_name = config.get('apis.{llm_source}.model_name'.format(llm_source=llm_source))
        llm_client = LLMAPIClient(api_key, model_name=model_name, base_url=base_url)
        tdw_obj = TemplateDrivenWriting(temp_base_dir=output_file, llm_client=llm_client)
        
        yield "🔧 初始化LLM客户端完成\n", None
        
        tdw_obj.set_template(input_template)
        yield f"📄 加载模板文件: {os.path.basename(input_template)}\n", None
        
        for input_resources in input_resources_list:
            tdw_obj.add_resource(input_resources)
            yield f"📚 添加参考资源: {os.path.basename(input_resources)}\n", None

        # 第一步：切割模版文档
        yield "\n🔍 第一步：分析文章结构...\n", None
        summary = ''
        partitions = []
        retry_count = 0
        while len(partitions) == 0 and retry_count < 3:  # 如果解析错误，重试一下
            retry_count = retry_count + 1
            yield f'📝 第{retry_count}次尝试解析文章结构...\n', None
            summary, partitions = tdw_obj.partitions()
            
        if len(partitions) == 0:
            yield '❌ 解析文章结构失败，请检查文章格式是否正确\n', None
            return
            
        yield f'✅ 文章摘要：{summary}\n', None
        yield f'✅ 文章结构解析成功，共{len(partitions)}个段落\n', None
        
        for i, partition in enumerate(partitions):
            yield f'   📋 段落{i+1}：{partition["summary"]}\n', None

        new_paragraphs = []
        # 第二~四步：每个段落串行处理
        yield f"\n🔄 开始处理各段落（共{len(partitions)}个）...\n", None
        
        for i, partition in enumerate(partitions):
            yield f'\n📝 开始处理第{i+1}个段落...\n', None
            
            if original_title != new_title:
                # 第二步： 生成写作指导
                retry_count = 0
                instruction = ''
                warnings = ''
                while instruction == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   🎯 第{retry_count}次尝试生成写作指导...\n', None
                    instruction, warnings = tdw_obj.object_replace(partition['slice'], original_title, new_title)
                    
                if instruction == '':
                    yield f'   ❌ 段落{i+1}生成写作指导失败，跳过该段落\n', None
                    continue
                    
                if isinstance(instruction, dict):
                    instruction = json.dumps(instruction, ensure_ascii=False, indent=2)
                if isinstance(warnings, dict):
                    warnings = json.dumps(warnings, ensure_ascii=False, indent=2)
                    
                yield f'   ✅ 写作指导生成成功\n', None
                yield f'   📋 写作指导：{instruction[:100]}...\n', None
                yield f'   ⚠️  注意事项：{warnings[:100]}...\n', None
                
                # 第三步： 搜索参考内容
                yield f'   🔍 开始搜索参考内容...\n', None
                ref = []
                retry_count = 0
                while len(ref) == 0 and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   🔍 第{retry_count}次尝试搜索参考内容...\n', None
                    ref = tdw_obj.search(instruction)
                    
                if len(ref) == 0:
                    yield f'   ❌ 段落{i+1}搜索参考内容失败，跳过该段落\n', None
                    continue
                    
                yield f'   ✅ 搜索到{len(ref)}个相关片段\n', None
                
                # 将搜索到的段落列表转换为字符串，只包含段落内容
                ref_text = "'''" + "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None]) + "'''"
                
                # 第3.2步： 段落重写指导
                retry_count = 0
                rewrite_instruction = ''
                while rewrite_instruction == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   📝 第{retry_count}次尝试生成重写指导...\n', None
                    rewrite_instruction = tdw_obj.rewrite_instruction(partition['summary'], original_title, new_title, ref_text, instruction, warnings)
                    
                if rewrite_instruction == '':
                    yield f'   ❌ 段落{i+1}重写写作指导失败，跳过该段落\n', None
                    continue
                    
                if isinstance(rewrite_instruction, dict):
                    rewrite_instruction = json.dumps(rewrite_instruction, ensure_ascii=False, indent=2)
                    
                yield f'   ✅ 重写指导生成成功\n', None
                
                # 第四步： 段落重写
                retry_count = 0
                new_paragraph = ''
                while new_paragraph == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   ✏️  第{retry_count}次尝试段落重写...\n', None
                    new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text, instruction)
                    
                if new_paragraph == '':
                    yield f'   ❌ 段落{i+1}重写失败，跳过该段落\n', None
                    continue
                    
                yield f'   ✅ 段落{i+1}重写成功\n', None
                yield f'   📄 重写后段落预览：{new_paragraph[:100]}...\n', None
                new_paragraphs.append(new_paragraph)
            else:
                # 如果标题相同，直接使用原段落
                new_paragraphs.append(partition['slice'])
                yield f'   ✅ 段落{i+1}保持原样（标题未变更）\n', None

        if len(new_paragraphs) == 0:
            yield '\n❌ 所有段落处理失败，无法生成文章\n', None
            return

        # 第五步：段落聚合
        yield f'\n📑 第五步：开始段落聚合（共{len(new_paragraphs)}个段落）...\n', None
        paragraphs_text = new_paragraphs
        paragraphs_text_printout = '\n\n'.join([f'段落{i+1}：\n{para}' for i, para in enumerate(paragraphs_text)])

        yield '✅ 段落聚合前准备完成\n', None
        
        new_article = tdw_obj.para_merge(paragraphs_text)
        yield '✅ 文章聚合成功\n', None
        
        # 第六步：文章润色
        yield '\n✨ 第六步：开始文章润色...\n', None
        polished = tdw_obj.polish(new_article)
        yield '✅ 文章润色成功\n', None
        
        yield '\n🎉 所有处理步骤完成！\n', None
        
        # 返回最终结果
        yield '✅ 处理完成，正在返回结果...\n', (str(paragraphs_text_printout), str(new_article), str(polished))
        
    except Exception as e:
        yield f'\n❌ 处理过程中发生错误：{str(e)}\n', None
        return


if __name__ == '__main__':
    # 测试流式API
    input_template = 'asset/天津市债务融资工具总体情况.docx'
    input_resources_list = ['asset/北京市债务融资工具发展报告：历史演进、2024年现状与未来展望.docx']
    output_file = 'output'
    original_title = '天津市'
    new_title = '北京市'
    
    for log_msg, results in tdw_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
        print(log_msg, end='')
        if results is not None:
            print("处理完成！")
            break
